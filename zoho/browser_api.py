from typing import Any, Dict, Optional

import httpx
from httpx import HTTPStatusError
from loguru import logger
from pydantic import BaseModel


class ZohoAPIError(HTTPStatusError):
    """Zoho API error with code, details, and message"""

    def __init__(
        self,
        message: str,
        code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        request: Optional[httpx.Request] = None,
        response: Optional[httpx.Response] = None,
    ):
        """
        Initialize Zoho API error

        Args:
            message: error message
            code: error code from Zoho API
            details: error details from Zoho API
            request: original HTTP request
            response: HTTP response
        """
        super().__init__(message, request=request, response=response)
        self.message = message
        self.code = code
        self.details = details or {}

    def __str__(self) -> str:
        """string representation of the error"""
        if self.code:
            return f"ZohoAPIError(code={self.code}, message={self.message})"
        return f"ZohoAPIError(message={self.message})"

    def __repr__(self) -> str:
        """detailed representation of the error"""
        return (
            f"ZohoAPIError(status_code={self.response.status_code}, "
            f"message={self.message!r}, code={self.code!r}, details={self.details!r})"
        )


class ZohoBrowserAPI:
    """Zoho 浏览器 API 客户端 - 用于直接调用 crm.zoho.com 接口"""

    _client: Optional[httpx.AsyncClient] = None
    base_url: str = "https://crm.zoho.com"
    timeout: float = 30.0
    user_agent: str = (
        "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 "
        "(KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36"
    )
    org_id: str = "663291548"

    def __init__(self, iamadt: str, iambdt: str, csrf_token: str):
        """
        初始化客户端

        Args:
            timeout: 请求超时时间
        """
        self.iamadt = iamadt
        self.iambdt = iambdt
        self.csrf_token = csrf_token

    async def handle_response(self, response: httpx.Response):
        """handle response"""
        if response.is_success:
            return

        await response.aread()
        response_data = response.json()

        if "code" in response_data:
            raise ZohoAPIError(
                message=response_data.get("message", "unknown error"),
                code=response_data.get("code"),
                details=response_data.get("details", {}),
                request=response.request,
                response=response,
            )
        response.raise_for_status()

    async def _add_headers(self, request: httpx.Request) -> None:
        """format headers before sending request"""
        request.headers["User-Agent"] = self.user_agent
        request.headers["X-CRM-ORG"] = self.org_id
        request.headers["X-ZCSRF-Token"] = f"crmcsrfparam={self.csrf_token}"
        request.headers["Cookie"] = f"_iamadt={self.iamadt}; _iambdt={self.iambdt}; crmcsr={self.csrf_token}"

    @property
    def client(self) -> httpx.AsyncClient:
        """获取或创建异步客户端"""
        if self._client is None:
            logger.info(f"create httpx.AsyncClient with base_url: {self.base_url}, timeout: {self.timeout}")
            self._client = httpx.AsyncClient(
                base_url=self.base_url,
                timeout=self.timeout,
                event_hooks={"request": [self._add_headers], "response": [self.handle_response]},
            )
        return self._client

    async def close(self):
        """关闭客户端连接"""
        if self._client:
            await self._client.aclose()
            self._client = None

    async def get_contact_by_id(self, contact_id: str) -> dict:
        """获取联系人信息"""
        response = await self.client.get(f"/crm/v2.2/Contacts/{contact_id}")
        data = response.json().get("data", [])
        if data and isinstance(data, list) and len(data) > 0:
            return data[0]
        raise ValueError(f"contact not found: {contact_id}")

    async def update_contact_by_id(self, contact_id: str, contact: dict | BaseModel):
        """更新联系人信息"""

        if isinstance(contact, BaseModel):
            contact = contact.model_dump(by_alias=True, exclude_none=True)
        response = await self.client.patch(
            f"/crm/v2.2/Contacts/{contact_id}",
            json={"data": [contact | {"id": contact_id}], "skip_mandatory": False},
        )
        return response.json()
