from typing import Dict, Literal

from pydantic import BaseModel, Field


class ZohoContactData(BaseModel):
    """
    Represents the transformed contact data ready to be used in Zoho CRM.
    This model combines data from Apollo, default settings, and LLM-summarized information.
    """

    # salutation: Literal["Mr.", "Mrs.", "Ms.", "Dr.", "Prof."] | None = Field(
    #     None, alias="Salutation", description="The salutation for the contact."
    # )

    first_name: str | None = Field(None, alias="First_Name", description="The first name of the contact.")
    last_name: str | None = Field(None, alias="Last_Name", description="The last name of the contact.")
    email: str | None = Field(None, alias="Email", description="The primary email address of the contact.")
    title: str | None = Field(None, alias="Title", description="The job title of the contact.")

    phone: str | None = Field(None, alias="Phone", description="The primary phone number of the contact.")
    mobile: str | None = Field(None, alias="Mobile", description="The mobile number of the contact.")

    department: str | None = Field(
        ...,
        alias="Department",
        max_length=50,
        description="""The department(s) of the contact, derived from Apollo's 'departments' field.
        Multiple departments are joined by ', '. The string length must not exceed 50 characters.
        Ensure the department names are properly capitalized and formatted as required by Zoho CRM.
        e.g. 'Sales, Marketing, Sales Operations'""",
    )

    linkedin: str | None = Field(None, alias="LinkedIn", description="The LinkedIn profile URL of the contact.")
    twitter: str | None = Field(
        None, alias="Twitter", description="The Twitter handle of the contact, extracted from the Twitter URL."
    )
    facebook: str | None = Field(None, alias="Facebook", description="The Facebook profile URL of the contact.")

    region: Literal["-None-", "EMEA", "NAM", "LATAM", "APAC"] | None = Field(
        None,
        alias="Region",
        description="""The region of the contact, derived from Apollo's 'country' field.
        One of the values from REGION_LIST: ['-None-', 'EMEA', 'NAM', 'LATAM', 'APAC']""",
    )

    mailing_city: str | None = Field(None, alias="Mailing_City", description="The city in the mailing address.")
    mailing_state: str | None = Field(
        None,
        alias="Mailing_State",
        examples=["New York(NY)", "California(CA)", "Sichuan(SC)"],
        description="""The state in the mailing address. Use two-letter abbreviation to full state name if available,
        e.g. New York(NY) or California(CA), or Sichuan(SC).""",
    )
    mailing_street: str | None = Field(None, alias="Mailing_Street", description="The street in the mailing address.")
    mailing_zip: str | None = Field(
        None,
        alias="Mailing_Zip",
        examples=["10001", "94105", "90210"],
        description="""The zip code in the mailing address, which means postal code or zip code in some countries.
        Lookup from the state and city if no postal_code is provided.""",
    )

    rec_reason: str | None = Field(
        None,
        alias="Rec_Reason",
        description="""The recommendation reason for the contact.
        Derived from Apollo's 'thinking' field or summarized by the LLM.""",
    )

    level: Literal["low", "medium", "high"] | None = Field(
        None,
        alias="Level",
        description="""The priority level of the contact.""",
    )

    # Hardcoded or default values
    newsletter_signup: str | None = Field(None, alias="Newsletter_Signup", description="Newsletter signup status.")
    country_territory: str | None = Field(None, alias="Country_Territory", description="The country of the contact.")
    exchange_rate: float | None = Field(None, alias="Exchange_Rate", description="The exchange rate.")
    function_type: (
        Literal[
            "Business Entry",
            "Business Management",
            "Business Senior Management",
            "Technical Entry",
            "Technical Management",
            "Technical Senior Management",
        ]
        | None
    ) = Field(
        None,
        alias="Function_Type",
        description="The function type of the contact.",
    )

    description: str | None = Field(None, alias="Description", description="The description of the contact.")

    secondary_email: str | None = Field(None, alias="Secondary_Email", description="Secondary email address.")
    currency: str | None = Field(None, alias="Currency", description="The currency associated with the contact.")
    labels: str | None = Field(None, alias="Labels", description="Labels for the contact.")

    # use "AI Research" for AI agent-detected contacts
    lead_source: str | None = Field("AI Research", alias="Lead_Source", description="The lead source for the contact.")

    # {"id": "3091799000000091033"}
    layout: Dict[str, str] | None = Field(None, alias="Layout", description="Layout information for Zoho CRM.")
