from typing import Optional

import httpx
from loguru import logger

from config import (
    MAX_KEEPALIVE_CONNECTIONS,
    ZOHO_API_HOST,
    ZOHO_API_KEY,
)

zoho_client = httpx.AsyncClient(
    base_url=ZOHO_API_HOST,
    timeout=30.0,
    headers={"x-api-key": ZOHO_API_KEY},
    limits=httpx.Limits(max_keepalive_connections=MAX_KEEPALIVE_CONNECTIONS),
)


async def query_contacts_from_zoho(account_name: str, account_id: Optional[str] = None) -> list[dict]:
    """调用 zoho-api 获取查询 Account 下的联系人"""
    try:
        query_params = {"accountName": account_name}
        if account_id:
            query_params = {"accountId": account_id}

        response = await zoho_client.get("/api/crm/zoho/account-contacts", params=query_params)

        if response.status_code == 200:
            results = response.json()
            return results.get("result", [])
        else:
            logger.error(f"调用 zoho-api 失败 - 状态码: {response.status_code}, 响应: {response.text}")
    except Exception as e:
        logger.error(f"调用 zoho-api 发生异常: {e}")

    return []


async def get_existing_contacts(
    account_name: str,
    account_id: Optional[str] = None,
) -> list[dict]:
    """获取已存在的联系人"""
    logger.info(f"account_name: {account_name}, account_id: {account_id}")
    if account_name is None and account_id is None:
        return []

    try:
        contacts = await query_contacts_from_zoho(account_name, account_id)
        return [
            {
                "name": contact["full_name"],
                "title": contact.get("title", ""),
                "email": contact.get("email", ""),
                "linkedin_url": contact.get("linkedIn", ""),
                "id": contact.get("id", ""),
            }
            for contact in contacts
        ]
    except Exception as e:
        logger.error(f"从 zoho 平台获取联系人发生异常：{e}", exc_info=True)

    return []


# 使用示例
if __name__ == "__main__":
    import asyncio
    import json

    async def main():
        results = await get_existing_contacts("Swoop")
        logger.info(json.dumps(results, indent=4))

    asyncio.run(main())
