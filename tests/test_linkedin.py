import pytest

from agent.tools.linkedin import agentic_search_people, rapidapi
from utils.logging_callback_handler import logging_callback


async def test_search_people_api():
    result = await rapidapi.search_people(keywords="<PERSON>", title="R&D CTO Office")
    print(result)
    data = result.get("data")
    assert data is not None
    assert result.get("error") is None
    assert len(data) > 0
    assert data[0].get("fullName") is not None
    assert data[0].get("headline") is not None
    assert data[0].get("summary") is not None
    assert data[0].get("profilePicture") is not None
    assert data[0].get("location") is not None
    assert data[0].get("profileURL") is not None


async def test_agentic_search_people():
    with logging_callback():
        result = await agentic_search_people("<PERSON> Aureyre @ Schindler")
        print(result)
        assert result is not None
        assert result.profile_url == "https://www.linkedin.com/in/laurent-aureyre"


async def test_agentic_search_people_multi_results():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("<PERSON>")
            print(result)


async def test_agentic_search_people_no_results():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("Harley Crxeam")
            print(result)


async def test_agentc_search_people_wrong_query():
    with pytest.raises(ValueError):
        with logging_callback():
            result = await agentic_search_people("Laurent Aureyre @ InHand Networks")
            print(result)
