from langchain.tools import tool
from langchain_core.messages import ToolMessage
from langgraph.prebuilt import create_react_agent

from agent.tools.final_answer import create_final_answer_tool
from utils import init_model


async def test_agent_final_answer_tool():
    final_answer = create_final_answer_tool(
        schema={
            "type": "object",
            "properties": {"city": {"type": "string"}, "weather": {"type": "string"}},
            "required": ["city", "weather"],
            "additionalProperties": False,
        }
    )

    @tool
    def get_weather():
        """
        Get the weather of Beijing
        """
        return "Rainy"

    agent = create_react_agent(
        model=init_model("gpt-4.1-mini"),
        tools=[final_answer, get_weather],
        prompt="What is the weather of Beijing?",
    )
    result = await agent.ainvoke({})
    message = result.get("messages")[-1]

    assert isinstance(message, ToolMessage)
    assert isinstance(message.content, str)
    assert isinstance(message.artifact, dict)
    assert message.artifact == {"city": "Beijing", "weather": "Rainy"}
