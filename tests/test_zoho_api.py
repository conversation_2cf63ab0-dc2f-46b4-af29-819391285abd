from pprint import pformat, pprint

import pytest
import yaml
from loguru import logger

from agent.tools.apollo import apollo
from agent.tools.linkedin import agentic_search_people
from utils.extractors import extract_structure_response
from zoho.browser_api import <PERSON><PERSON><PERSON><PERSON><PERSON>rro<PERSON>, ZohoBrowserAPI
from zoho.contacts_api import get_contact_by_id
from zoho.models import ZohoContactData


async def test_get_contact_by_id():
    contact_id = "3091799000312890082"
    contact = await get_contact_by_id(contact_id)
    print(contact)


async def test_enrich_contact_info():
    contact_id = "3091799000312028001"
    contact = await get_contact_by_id(contact_id)
    logger.info(f"contact: {pformat(contact)}")

    linkedin_url = contact.get("linkedIn")
    if not linkedin_url:
        searched_person = await agentic_search_people(yaml.dump(contact, allow_unicode=True))
        logger.info(f"searched person from linkedin: {searched_person}")
        linkedin_url = searched_person.profile_url

    person = await apollo.enrich_person(linkedin_url=linkedin_url)
    logger.info(f"enriched person: {pformat(person)}")

    assert person is not None
    for key in ["employment_history", "organization"]:
        person.pop(key)
    person_str = yaml.dump(person, allow_unicode=True)

    contact = await extract_structure_response(
        person_str,
        ZohoContactData,
        # model="gemini-2.5-flash-instant",
        # model="gpt-5-mini-minimal",
        instructions="""
- Unless explicitly asked, only output the value if you are very confident about it, don't make any assumptions.
- Derive the zip code from state and city.
        """,
    )
    logger.info(f"fields to update: {contact.model_dump_json(indent=2, by_alias=True, exclude_none=True)}")

    client = ZohoBrowserAPI(iam_adt, iam_bdt, csrf_token)
    response = await client.update_contact_by_id(contact_id, contact)
    pprint(response)


csrf_token = "a83fc68b408408cd8e9d614591a67ab5c0f4169fa93d62d88fbc8f0a95e8665915cbfec67fa38105c98832cd94c3aa6461acbf1fbbe90b889f37a6d360b9e048"  # noqa: E501
iam_adt = "998ed570356a6106b737dcddf335a145a150658af78269d0df4fcfe8f57ba5b27f3458b192ab9cdbe320dc5c655aaedd"  # noqa: E501
iam_bdt = "e4afbc3ef79139314aaaddcdc04fe1c534d537b18e00a92726284fa2da617061888d24f0c7c0a038d256b543513ba97e84c3babba08ebe80ca8b8f46d30949c5"  # noqa: E501


async def test_zoho_browser_api():
    client = ZohoBrowserAPI(iam_adt, iam_bdt, csrf_token)
    response = await client.get_contact_by_id("3091799000312992006")
    pprint(response, indent=2, compact=True)


async def test_zoho_api_error():
    client = ZohoBrowserAPI(iam_adt, iam_bdt, csrf_token)
    with pytest.raises(ZohoAPIError) as e:
        await client.get_contact_by_id("30917990003129920061")
    assert e.value.code == "INVALID_URL_PATTERN"


async def test_update_contact_by_id():
    client = ZohoBrowserAPI(iam_adt, iam_bdt, csrf_token)
    response = await client.update_contact_by_id("3091799000312992006", {"first_name": "test"})
    pprint(response)
