import pytest
import yaml
from loguru import logger

from agent.account.analyze_company_similarity import analyze_company_similarity
from agent.account.models import CompanyBasicInfo
from agent.account.research_company import analyze_ideal_customer_profile, gemini_research_company
from agent.account.search_lookalike_candidates_pool import filter_irrelevant_organizations
from agent.tools.apollo import apollo, iter_apollo_page_result
from utils import logging_callback
from utils.utils import as_completed_with_concurrency
from zoho.accounts_api import ZohoAccountInfo, fetch_accounts_info_by_account_id

# user_query = "请推荐在美国与Robertson’s相似的混凝土公司，这些公司同样运营搅拌车车队，并可通过车载网关设备实现车辆远程监控。推荐10家公司。" # noqa: E501
user_query = "帮我找像这家公司一样的空气压缩机的生产厂商，并且有可能会用到我司的边缘计算设备或者 IG 设备的公司"


icp = """
1. 客户画像梳理
- 基础信息：行业为混凝土生产与供应，规模中型（约94名员工），地域主要在美国加利福尼亚南部及内华达州，成立于1969年，发展阶段成熟稳定。
- 组织特征：业务模式为混凝土及骨料产品的生产与配送，拥有自营搅拌车车队，组织架构包含运营、IT支持等部门。
- 技术环境：采用GPS车载网关设备实现车辆远程监控，技术栈包括无线车辆追踪系统，数字化程度较高，IT预算支持车队管理及远程监控技术。

2. 痛点深度挖掘
- 业务痛点：运输成本高，车队调度效率需提升，混凝土质量和交付时间要求严格。
- 技术痛点：现有车辆管理系统需支持实时定位和远程监控，确保车队运营透明化。
- 成本痛点：人力调度成本高，设备维护和运营成本压力大。
- 痛点优先级：运输成本控制（高），车队调度效率（中高），技术系统稳定性（中）。

3. 关键成功特征
- 触发事件：车队规模扩大，运输成本上升，需提升运营效率和透明度。
- 应用场景：搅拌车车队的实时位置监控、运输路径优化、车辆状态远程监测。
- 决策因素：设备稳定性、系统集成能力、成本效益、供应商服务支持。
- 预算特征：中等IT预算，采购周期中等，审批流程涉及运营和IT部门。
- 内部推动者：车队运营经理、IT部门负责人、财务主管。

4. Lookalike 筛选条件
- 必备条件：美国境内混凝土生产企业，拥有自营搅拌车车队，具备车辆远程监控需求。
- 优选条件：车队规模在50辆以上，已有一定数字化管理基础，关注运输成本和运营效率。
- 排除条件：无自营车队、仅依赖第三方运输、无远程监控需求的企业。
- 验证指标：车队车辆数、IT系统投入比例、运输成本占比、数字化管理成熟度。
"""  # noqa: E501


async def test_prospecting_account():
    from server.account.prospecting_accounts_processor import ProspectingAccountsProcessor

    processor = ProspectingAccountsProcessor(task_id="test_prospecting_account")
    result = await processor.ainvoke(
        input={
            "account_id": "3091799000303764040",
            "user_query": "我想找一些像Mesta Electronics样的需要使用边缘网关产品的公司，我想把我们的边缘网关产品卖给他们，只帮我找出来3家。",  # noqa: E501
        }
    )
    print(result)


@pytest.mark.env("ICP_CACHE_ENABLED=False")
async def test_analyze_ideal_customer_profile():
    with logging_callback():
        # user_query = "帮我找一下类似solwise这种欧洲的teltonika的distributor/reseller/SI，用中文回答"
        # zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000241625011")
        # zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000303263001")
        user_query = "I want to know companies like CTS Mobility located in Texas, Louisiana, Arkansas and Oklahoma that resale cellular routers to business customers, 回复中文"  # noqa: E501
        zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000154941135")

        user_query = "I want to find companies like OnMed"  # noqa: E501

        icp_analysis_result = await analyze_ideal_customer_profile(
            user_query=user_query + ", respond in Chinese",
            lookalike_company="OnMed http://www.onmed.com",
        )
        print(icp_analysis_result)
        assert icp_analysis_result is not None


@pytest.mark.env("ICP_CACHE_ENABLED=False")
async def test_analyze_ideal_customer_profile_company_not_found():
    with logging_callback():
        user_query = (
            "I want to find more ITS system integrators similar to Smart Tek LLC in Southeast region. 用中文回答"
        )
        zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000291953103")

        icp_analysis_result = await analyze_ideal_customer_profile(
            user_query=user_query,
            lookalike_company=f"{zoho_account_info['name']} {zoho_account_info.get('website')}",
        )
        print(icp_analysis_result)
        assert icp_analysis_result is not None


async def test_search_lookalike_companies():
    from agent.account.search_lookalike_companies import search_lookalike_companies

    with logging_callback():
        # user_query = "请推荐在美国与Robertson’s相似的混凝土公司，这些公司同样运营搅拌车车队，并可通过车载网关设备实现车辆远程监控。10家"  # noqa: E501
        # user_query = "找类似这家公司，做电信行业的，可能会代理我们的企业网络产品，如 ER 路由器的公司。"
        # user_query = "帮我找一下类似solwise这种欧洲的distributor/reseller/SI，在卖我们的竞品的产品的公司，不限制规模，用中文回答" # noqa: E501
        # user_query = (
        #     "I want to find more ITS system integrators similar to Smart Tek LLC in Southeast region. 用中文回答."
        # )
        # zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000291953103")

        # user_query = "please find me other mid to large size companies similar to Klika Tech and provide those especially focused on Edge Computer deployments, 用中文回答" # noqa: E501
        # zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000312300001")

        user_query = "I want to find similar companies (system integrator in transportation) in Norway，用中文回答"
        zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000126277001")

        apollo_company_info = await apollo.get_complete_organization_info(
            query=f"{zoho_account_info['name']} {zoho_account_info.get('website')}"
            # query="www.onmed.com"
        )

        icp_analysis_result = await analyze_ideal_customer_profile(
            user_query=user_query,
            lookalike_company=yaml.dump(apollo_company_info, indent=2, allow_unicode=True),
        )

        logger.info(f"ICP analysis result: {icp_analysis_result}")
        result = await search_lookalike_companies(
            user_query=user_query,
            apollo_company_info=apollo_company_info,
            ideal_customer_profile=icp_analysis_result,
            excludes=[],
        )
        print(result)
        assert result is not None
        for index, company in enumerate(result.get("lookalike_companies", [])):
            print(f"No. {index + 1}")
            print(f"公司名称: {company.get('name')}")
            print(f"公司网站: {company.get('website')}")
            print(f"公司类型: {company.get('account_type')}")
            print(f"公司电话: {company.get('phone')}")
            print(f"公司所属行业: {company.get('industry')}")
            print(f"公司市场细分: {company.get('market_segments')}")
            print(f"公司所在地: {company.get('territory')}")
            print(f"公司所在州/省: {company.get('address_state')}")
            print(f"公司描述: {company.get('description')}")
            print("-" * 50)


async def test_xx():
    user_query = "Can I have similar companies that provide energy solutions like Vestas in South America，用中文回答"
    zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000312273001")

    lookalike_company_desc = f"Company Name: {zoho_account_info['name']}"
    if zoho_account_info.get("website"):
        lookalike_company_desc += f", Website: {zoho_account_info.get('website')}"

    icp_analysis_result = await analyze_ideal_customer_profile(
        user_query=user_query,
        lookalike_company=lookalike_company_desc,
    )

    response = await apollo.search_organization(
        q_organization_keyword_tags=[
            "wind farm",
            "wind project",
            "parque eolico",
            "parques eolicos",
            "desarrollador eolico",
            "EPC eolico",
            "desenvolvedor eolico",
            "EPC eólico",
        ],
        organization_locations=["South America"],
        organization_num_employees_ranges=["201-500", "501-1000"],
    )

    # print(response.pagination)

    print(f"user query: {user_query}")
    print(f"reference company: {icp_analysis_result}")
    print(f"companies: {response.data}")


async def test_apollo_get_company_lookalikes():
    user_query = "I want to find companies like OnMed"  # noqa: E501

    icp_analysis_result = await analyze_ideal_customer_profile(
        user_query=user_query + ", respond in Chinese",
        lookalike_company="Company Name: OnMed, Website: http://www.onmed.com",
    )

    print(icp_analysis_result)

    passed_companies = []
    failed_companies = []
    count = 0
    max_count = 10
    with logging_callback():
        async for companies in iter_apollo_page_result(
            apollo.search_organization,
            limit=20,
            lookalike_organization_ids=["5e50ebc0bc989600d6e01fc1"],
            organization_locations=["United States"],
            q_organization_keyword_tags=[
                "telemedicine",
                # "virtual health kiosk",
                "clinic-in-a-box",
            ],
            include_snippets=True,
        ):
            companies, _ = await filter_irrelevant_organizations(
                organizations=companies,
                user_query=user_query,
                reference_company=icp_analysis_result,
            )

            coros = [
                analyze_company_similarity(
                    id=company.get("id"),
                    name=company.get("name"),
                    website_url=company.get("website_url"),
                    user_query=user_query,
                    reference_company=icp_analysis_result,
                )
                # for company in filtered_companies
                for company in companies
            ]

            tasks = as_completed_with_concurrency(n=2, coros=coros)
            for task in tasks:
                try:
                    result: dict = await task
                    if result["passed"]:
                        passed_companies.append(result)
                        logger.success(
                            f"Passed company: {result['company_name']}, "
                            f"{result['company_info'].get('website_url', '')}, "
                            f"{result['company_info'].get('linkedin_url', '')}, "
                            f"{count + 1} / {len(passed_companies) + len(failed_companies)}"
                        )
                        count += 1
                        if count >= max_count:
                            break
                    else:
                        failed_companies.append(result)
                        logger.warning(f"Skipped company: {result['company_name']}")
                except Exception as e:
                    logger.error(f"Error: {e}")
                    continue

            if count >= max_count:
                break

        logger.info(
            f"Sub-keyword set passed {len(passed_companies)}/{len(passed_companies) + len(failed_companies)} companies"  # noqa: E501
        )

        for company in passed_companies:
            company_info = company["company_info"]
            print(
                f"{company_info['name']} - {company_info.get('website_url', '')} \n"
                f"{company_info.get('linkedin_url', '')} \n"
                f"{company_info.get('short_description', '')} \n"
                f"{company_info.get('location', '')} \n"
                f"{company_info.get('industry', '')} \n"
            )
            print("-" * 50)


async def test_search_params():
    from agent.account.search_lookalike_candidates_pool import filter_irrelevant_organizations

    with logging_callback():
        # 模板公司：keywords: [1, 2, 3, 4], locations: [1, 2, 3, 4], industry: [1, 2, 3, 4]

        # keyword: 1 + industry =>
        params = {
            "organization_locations": ["Oklahoma", "Texas", "Louisiana", "Arkansas"],
            "q_organization_keyword_tags": ["managed network services"],
            "organization_industry_tag_ids": ["5567cd4773696439b10b0000"],
        }

        user_query = "I want to know companies like CTS Mobility located in Texas, Louisiana, Arkansas and Oklahoma that resale cellular routers to business customers"  # noqa: E501
        zoho_account: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000311732007")

        lookalike_company_desc = f"Company Name: {zoho_account['name']}"
        if zoho_account.get("website"):
            lookalike_company_desc += f", Website: {zoho_account.get('website')}"

        icp_analysis_result = await analyze_ideal_customer_profile(
            user_query=user_query + ", respond in Chinese",
            lookalike_company=lookalike_company_desc,
        )

        passed_companies = []
        failed_companies = []
        count = 0
        max_count = 10

        async for companies in iter_apollo_page_result(apollo.search_organization, limit=max_count, **params):
            if False:
                filtered_companies, _ = await filter_irrelevant_organizations(
                    organizations=companies,
                    user_query=user_query,
                    reference_company=icp_analysis_result,
                    search_params=params,
                )
            else:
                filtered_companies = companies

            coros = [
                analyze_company_similarity(
                    id=company.get("id"),
                    name=company.get("name"),
                    website_url=company.get("website_url"),
                    user_query=user_query,
                    reference_company=icp_analysis_result,
                    search_params=params,
                )
                # for company in filtered_companies
                for company in filtered_companies
            ]

            tasks = as_completed_with_concurrency(n=5, coros=coros)
            for task in tasks:
                try:
                    result: dict = await task
                    if result["passed"]:
                        passed_companies.append(result)
                        logger.debug(
                            f"Passed company: {result['company_name']}, "
                            f"{count + 1} / {len(passed_companies) + len(failed_companies)}"
                        )
                        count += 1
                        if count >= max_count:
                            break
                    else:
                        failed_companies.append(result)
                        logger.warning(f"Skipped company: {result['company_name']}")
                except Exception as e:
                    logger.error(f"Error: {e}")
                    continue

            if count >= max_count:
                break

        logger.info(
            f"Sub-keyword set passed {len(passed_companies)}/{len(passed_companies) + len(failed_companies)} companies"  # noqa: E501
        )

        # 分析搜索条件问题
        from utils import init_model

        analysis_llm = init_model(model="o3")

        # 准备失败和成功公司的信息（显示完整信息）
        failed_companies_info = failed_companies[:20]  # 限制前20个避免太长
        passed_companies_info = passed_companies

        analysis_prompt = f"""请分析这次搜索结果，找出搜索条件可能存在的问题：

## 用户要求
{user_query}

## 类似公司要求
{icp_analysis_result}

## 搜索条件
关键词标签: {params["q_organization_keyword_tags"]}
地理位置: {params["organization_locations"]}
员工数量范围: {params.get("organization_num_employees_ranges")}

## 搜索结果统计
- 通过筛选的公司数量: {len(passed_companies)}
- 被筛掉的公司数量: {len(failed_companies)}
- 总搜索数量: {len(passed_companies) + len(failed_companies)}

## 成功的公司（通过筛选）
{yaml.dump(passed_companies_info, allow_unicode=True, default_flow_style=False)}

## 失败的公司（被筛掉）
{yaml.dump(failed_companies_info, allow_unicode=True, default_flow_style=False)}

## 分析任务
请分析：
1. 搜索条件是否过于宽泛或过于狭窄？
2. 哪些关键词可能导致了不相关公司的出现？
3. 失败的公司都有什么共同特征？
4. 搜索条件应该如何优化来提高成功率？
5. 地理位置和员工数量范围是否合适？

请用中文回答，给出具体的优化建议。
keywords 是 includes 的关系，多个 keywords 是 OR 的关系。
根据未通过筛选的公司，分析出哪些关键词导致了不相关公司的出现，并给出优化建议。
以数组形式输出你建议删除的关键词，不要有任何解释。
"""

        try:
            analysis_result = await analysis_llm.ainvoke(analysis_prompt)
            print("\n" + "=" * 80)
            print("搜索条件分析结果:")
            print("=" * 80)
            print(analysis_result.content)
            print("=" * 80)
        except Exception as e:
            logger.error(f"Analysis failed: {e}")


async def test_analyze_company_similarity():
    from agent.account.search_lookalike_companies import analyze_company_similarity

    with logging_callback(quiet=False):
        user_query = "Can I have similar companies that provide energy solutions like Vestas，用中文回答"
        zoho_account_info: ZohoAccountInfo | None = await fetch_accounts_info_by_account_id("3091799000312273001")

        lookalike_company_desc = f"Company Name: {zoho_account_info['name']}"
        if zoho_account_info.get("website"):
            lookalike_company_desc += f", Website: {zoho_account_info.get('website')}"

        icp_analysis_result = await analyze_ideal_customer_profile(
            user_query=user_query,
            lookalike_company=lookalike_company_desc,
        )

        result = await analyze_company_similarity(
            id="54a11faa69702d97c16e1b02",
            name="NT Voice & Data Solutions",
            website_url="http://www.ntvoiceanddata.co.uk",
            user_query=user_query,
            reference_company=icp_analysis_result,
        )
        print(result)


async def test_research_company():
    from agent.account.research_company import research_company

    company_basic_info = CompanyBasicInfo(
        id="5da626db14833d0001428617",
        name="HolcimUS",
        website_url="http://www.holcim.us",
        linkedin_url="http://www.linkedin.com/company/holcimus",
        organization_revenue="30.6B",
        phone="******-372-1000",
        founded_year=2015,
    )

    with logging_callback():
        result = await research_company(
            company_basic_info=company_basic_info,
            user_query=user_query,
            ideal_customer_profile=icp,
        )
        assert result is not None


async def test_research_company_gemini():
    """Test the new research_company_gemini function"""

    # Test data

    company_basic_info = CompanyBasicInfo(
        # id="5f4758e646546600013a5f26",
        # name="Cemex U.S.",
        # website_url="http://www.cemexusa.com",
        # linkedin_url="http://www.linkedin.com/company/cemex-usa",
        # organization_revenue="4.1B",
        # phone="******-650-6200",
        # founded_year=1906,
        id="5da626db14833d0001428617",
        name="HolcimUS",
        website_url="http://www.holcim.us",
        linkedin_url="http://www.linkedin.com/company/holcimus",
        organization_revenue="30.6B",
        phone="******-372-1000",
        founded_year=2015,
    )

    with logging_callback():
        result = await gemini_research_company(
            company_basic_info=company_basic_info, user_query=user_query, ideal_customer_profile=icp
        )

        print("=== RESEARCH COMPANY GEMINI RESULT ===")
        print(f"Company name: {result.get('name')}")
        if "research_result" in result:
            print("Research completed successfully")
            print(f"Research result length: {len(result['research_result'])} characters")
            print("Research result preview:")
            print(result["research_result"])
        elif "error" in result:
            print(f"Research failed with error: {result['error']}")


async def test_extract_structure_response():
    from agent.account.search_lookalike_companies import LookalikeCompaniesResult
    from utils.extractors import extract_structure_response

    with logging_callback():
        result = await extract_structure_response(
            """
**eSIM Australia**
   - id: 66ee50103154e30001c6d552
   - name: eSIM Australia
   - website_url: http://www.esim.com.au
   - linkedin_url: http://www.linkedin.com/company/esim-australia
   - organization_revenue: 0.0
   - phone: null
   - founded_year: null
   - description: eSIM Australia provides local, regional, and global eSIMs with prepaid unlimited data plans. The company emphasizes flexibility, instant activation, and eco-friendly digital SIM solutions for travel and everyday use, supporting 5G networks and customer-centric service.
   - industry: telecommunications
   - location: Australia
            """,  # noqa: E501
            LookalikeCompaniesResult,
            model="gpt-4.1-mini",
            # model="gpt-5-mini-minimal",
        )
        print(yaml.dump(result.model_dump(mode="json"), allow_unicode=True))
