import asyncio
import json

from fastapi import APIRouter, HTTPException
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from agent.account.account_enrichment import get_enriched_account_info
from agent.account.models import SearchCompaniesResult
from agent.account.search_target_companies import search_target_companies
from server.account.add_account_to_zoho import add_account_to_crm_zoho
from server.account.prospecting_thread_manager import prospecting_thread_manager
from server.common_types import CurrentUser, StreamMessage, StreamMessageType
from zoho.accounts_api import AccountExistsException

router = APIRouter(tags=["account"])


class ProspectingAccountsRequest(BaseModel):
    account_id: str = Field(..., description="The ID of the account")
    user_query: str = Field(..., description="The user_query to use for the account")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class ProspectingMoreRequest(BaseModel):
    thread_id: str = Field(..., description="The thread ID")
    current_user: CurrentUser | None = Field(None, description="Current user information")


class AddAccountRequest(BaseModel):
    user_id: str = Field(..., description="The user ID")
    account_info: dict = Field(..., description="The account info")
    request_headers: dict = Field(..., description="The request headers")


@router.post("/prospecting-accounts")
async def prospecting_accounts(request: ProspectingAccountsRequest):
    """
    Prospecting accounts, start a new prospecting thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    account_id = request.account_id
    user_query = request.user_query
    current_user = request.current_user

    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    if not user_query:
        raise HTTPException(status_code=400, detail="User query is required")

    try:
        # 创建探查流程
        return await prospecting_thread_manager.create_thread(
            account_id=account_id,
            user_query=user_query,
            current_user=current_user,
        )
    except Exception as e:
        logger.error(f"Create prospecting thread failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Create prospecting thread failed: {str(e)}")


@router.post("/prospecting-accounts/more")
async def prospecting_accounts_more(request: ProspectingMoreRequest):
    """
    Prospecting more accounts on the existing thread
    """
    data = request.model_dump()
    logger.info(f"开始处理请求: {data}")

    thread_id = request.thread_id
    current_user = request.current_user
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")

    try:
        # 向探查流程中添加新的探查任务
        return await prospecting_thread_manager.add_thread_task(thread_id, current_user)
    except Exception as e:
        logger.error(f"Add prospecting thread task failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Add prospecting thread task failed: {str(e)}")


@router.get("/prospecting-accounts/{account_id}/threads")
async def get_prospecting_accounts_history(account_id: str):
    """
    Get the history of prospecting accounts
    """
    if not account_id:
        raise HTTPException(status_code=400, detail="Account ID is required")

    return await prospecting_thread_manager.list_all_prospecting_threads(account_id)


@router.get("/prospecting-accounts/thread/{thread_id}")
async def get_prospecting_accounts_task_profile(thread_id: str):
    if not thread_id:
        raise HTTPException(status_code=400, detail="Thread ID is required")
    try:
        return await prospecting_thread_manager.get_prospecting_thread_data(thread_id)
    except Exception as e:
        logger.error(f"Get prospecting data failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail=f"Get prospecting data failed: {str(e)}")


@router.post("/add-account")
async def add_account(request: AddAccountRequest):
    """Add a account to crm zoho"""
    logger.debug(f"收到添加account请求，request: {request.model_dump()}")

    user_id = request.user_id
    account_info = request.account_info
    request_headers = request.request_headers

    if not user_id:
        raise HTTPException(status_code=400, detail="User ID is required")

    if not account_info:
        raise HTTPException(status_code=400, detail="Account info is required")

    try:
        # 添加公司到 crm zoho
        account_id = await add_account_to_crm_zoho(
            account_info=account_info, user_id=user_id, request_headers=request_headers
        )
        return {"account_id": account_id}
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))


class AddAccountByUserInputRequest(BaseModel):
    user_input: str = Field(..., description="The user input")
    user_id: str = Field(..., description="The current user id")
    request_headers: dict = Field(..., description="The request headers")


@router.post("/add-account-by-user-input")
async def add_account_by_user_input(request: AddAccountByUserInputRequest):
    """Add an account to crm zoho by user input with SSE streaming"""

    def _build_stream_message(message: StreamMessage) -> str:
        return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"

    async def event_generator():
        try:
            user_input = request.user_input
            user_id = request.user_id
            request_headers = request.request_headers

            if not user_input:
                error_msg = StreamMessage.from_exception(Exception("User input is required"))
                yield _build_stream_message(error_msg)
                return

            if not user_id:
                error_msg = StreamMessage.from_exception(Exception("User ID is required"))
                yield _build_stream_message(error_msg)
                return

            if not request_headers:
                error_msg = StreamMessage.from_exception(Exception("Request headers is required"))
                yield _build_stream_message(error_msg)
                return

            yield _build_stream_message(StreamMessage.from_thinking("Searching for companies..."))

            # 搜索公司信息
            companies_result: SearchCompaniesResult = await search_target_companies(user_input)

            if not companies_result.companies:
                # 搜索不到结果
                error_msg = StreamMessage.from_exception(Exception("not_found"))
                yield _build_stream_message(error_msg)
                return

            elif len(companies_result.companies) > 1:
                # 搜索到多个结果
                data_msg = StreamMessage(
                    type=StreamMessageType.FINISH,
                    data={
                        "result": [company.model_dump(exclude_none=True) for company in companies_result.companies],
                    },
                )
                yield _build_stream_message(data_msg)
                return

            account_info = companies_result.companies[0]

            # 发送找到公司信息的消息
            company_found_msg = StreamMessage.from_thinking(
                f"I have found the target company. name: {account_info.name}, website: {account_info.website}"
            )
            yield _build_stream_message(company_found_msg)

            # 开始添加到CRM
            add_to_crm_msg = StreamMessage.from_thinking("Creating account in CRM system...")
            yield _build_stream_message(add_to_crm_msg)

            try:
                # 添加公司到 crm zoho
                account_id = await add_account_to_crm_zoho(
                    account_info=account_info.model_dump(exclude_none=True),
                    user_id=user_id,
                    request_headers=request_headers,
                )

                # 发送成功消息
                success_msg = StreamMessage.from_thinking(
                    f"I have already added the company to CRM system. account_id: {account_id}"
                )
                yield _build_stream_message(success_msg)

                # 发送完成消息
                finished_msg = StreamMessage(
                    type=StreamMessageType.FINISH,
                    data={"account_id": account_id},
                )
                yield _build_stream_message(finished_msg)

            except AccountExistsException as e:
                exists_msg = StreamMessage(
                    type=StreamMessageType.ERROR,
                    content="exists",
                    data={"account_id": e.account_id, "name": account_info.name},
                )
                yield _build_stream_message(exists_msg)
                return

            except Exception as e:
                logger.error(f"Add account to crm zoho failed: {e}", exc_info=True)
                yield _build_stream_message(StreamMessage.from_exception(Exception("unknown_error")))

        except asyncio.CancelledError:
            logger.info("Stream cancelled")
            return
        except Exception as e:
            logger.error(f"Stream error: {e}", exc_info=True)
            yield _build_stream_message(StreamMessage.from_exception("unknown_error"))

    return StreamingResponse(
        content=event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )


@router.get("/account/{account_id}/enrich")
async def enrich_account(account_id: str):
    """
    Enrich account information
    """
    try:
        return await get_enriched_account_info(account_id)
    except Exception as e:
        logger.error(f"Enrich account {account_id} failed: {e}", exc_info=True)
        raise HTTPException(status_code=400, detail={"error": "failed", "messages": "Failed to enrich account"})
