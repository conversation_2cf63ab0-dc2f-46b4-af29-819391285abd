from loguru import logger

from server.contacts.contact_enrichment_requests import contact_enrichment_requests
from zoho.contacts_api import update_contact_by_id
from zoho.zoho_notification import send_notification_to_zoho


async def handle_apollo_contact_webhook(request_id: str, request: dict):
    # 需要处理联系人webhook回调数据
    result = contact_enrichment_requests.get_info_by_request_id(request_id)
    if result is None:
        logger.warning(f"Can't find contact_id by request_id: {request_id}")
        return

    contact_id = result.get("contact_id", None)
    owner = result.get("owner", {})
    first_name = result.get("latest_data", {}).get("first_name", "")
    last_name = result.get("latest_data", {}).get("last_name", "")
    full_name = f"{first_name} {last_name}" if first_name and last_name else first_name or last_name or ""

    logger.info(
        f"request_id: {request_id} contact_id: {contact_id} full_name: {full_name} 收到webhook回调数据: {request}"
    )
    if contact_id is None:
        logger.warning(f"request_id: {request_id} contact_id is None, can't handle webhook callback data")
        return

    status = "failed"
    err_msg = ""
    contact_info = {}

    try:
        people_list = request.get("people", [])
        if not people_list or len(people_list) == 0:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            status = "failed"
            err_msg = f"{full_name}’s data was not found."
            return

        data = people_list[0]

        # 解析联系人电话信息
        if data.get("status", "") == "success":
            phone_numbers = data.get("phone_numbers", [])
            for phone_number in phone_numbers:
                if phone_number.get("type_cd", "") == "mobile":
                    contact_info["Mobile"] = phone_number.get("sanitized_number", "")
                else:
                    contact_info["Phone"] = phone_number.get("sanitized_number", "")

        if contact_info.get("Mobile", None) is None and contact_info.get("Phone", None) is None:
            logger.warning(f"获取到的联系人 {contact_id} 扩充信息为空")
            status = "failed"
            err_msg = f"{full_name}’s phone number was not found."
            return

        try:
            await update_contact_by_id(contact_id, contact_info)
            status = "success"
            # 通知用户已经更新了联系人电话
        except Exception as e:
            logger.error(f"调用 zoho-api 更新联系人 {contact_id} 信息失败: {e}", exc_info=True)
            status = "failed"
            err_msg = str(e)

    except Exception as e:
        status = "failed"
        logger.error(f"处理联系人 {contact_id} 扩充信息的webhook回调数据失败: {e}", exc_info=True)
        err_msg = "Update contact phone failed"

    finally:
        # 更新联系人扩充状态
        try:
            contact_enrichment_requests.update_contact_enrichment_status(
                contact_id,
                request_id,
                status,
                error=err_msg if status == "failed" else None,
                phone_info=contact_info if status == "success" else None,
            )
            # notification
            if owner and owner.get("email") is not None:
                await send_notification_to_zoho(
                    {
                        "message": err_msg
                        if status == "failed"
                        else f"The contact {full_name}'s phone number has been successfully updated.",
                        "email": owner.get("email"),
                        "title": "AI Agent",
                    }
                )

        except Exception as e:
            logger.error(f"更新联系人 {contact_id} 扩充状态失败: {e}", exc_info=True)
