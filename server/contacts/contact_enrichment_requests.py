from datetime import datetime, timedelta
from typing import Any, Dict, Optional

from loguru import logger
from pymongo import ASCENDING, DESCENDING
from pymongo.collection import Collection

from server.common_types import ContactEnrichmentStatus
from server.task.mongodb_client import mongodb_client


class ContactEnrichmentRequests:
    """
    contact enrichment requests with mongodb storage.
    """

    def __init__(self):
        """初始化 MongoDB 存储"""
        self.db = mongodb_client.db
        self.collection: Collection = self.db["contact_enrichment_requests"]
        self._setup_indexes()

    def _setup_indexes(self):
        try:
            # 先删除可能存在的旧索引
            try:
                self.collection.drop_index("contact_id_idx")
                logger.info("已删除旧的 contact_id 索引")
            except Exception:
                pass  # 索引不存在，忽略错误

            # 创建复合索引：contact_id + updated_at（降序），优化最新记录查询
            self.collection.create_index(
                [("contact_id", ASCENDING), ("updated_at", DESCENDING)],
                name="contact_id_updated_at_idx",
                background=True,
            )
            logger.info("已创建 contact_id + updated_at 复合索引")

            # 为 _id (request_id) 查询保留单独索引（MongoDB 默认已有）

        except Exception as e:
            logger.error(f"设置索引失败: {e}", exc_info=True)
            raise

    def get_contact_id_by_request_id(self, request_id: str) -> str | None:
        """根据request_id获取contact_id"""
        try:
            contact_request = self.collection.find_one({"_id": request_id})
            if contact_request is None:
                logger.warning(f"Can't find contact_id by request_id: {request_id}")
                return None
            return contact_request.get("contact_id", None)
        except Exception as e:
            logger.error(f"Query contact_id by request_id {request_id} failed: {e}", exc_info=True)
            raise Exception(f"Query contact_id by request_id failed: {str(e)}")

    def get_info_by_request_id(self, request_id: str) -> str | None:
        """根据request_id获取 owner"""
        try:
            result = self.collection.find_one({"_id": request_id})
            if result is None:
                logger.warning(f"Can't find records of request_id: {request_id} ")
                return None
            return result
        except Exception as e:
            logger.error(f"Query request_id {request_id} 对应的 owner 失败: {e}", exc_info=True)
            raise Exception(f"Query owner by request_id failed: {str(e)}")

    def get_contact_enrichment_data(self, contact_id: str, check_expiry: bool = True) -> Dict[str, Any] | None:
        """
        根据contact_id获取联系人扩充数据和状态 (返回最新的记录)

        Args:
            contact_id: 联系人ID
            check_expiry: 是否检查状态过期，默认为True

        Returns:
            包含完整扩充数据的字典，如果不存在返回None
        """
        try:
            # 按照 updated_at 降序排序，获取最新的一条记录
            enrichment_data = self.collection.find_one({"contact_id": contact_id}, sort=[("updated_at", DESCENDING)])

            if enrichment_data is None:
                logger.warning(f"Can't find enrichment data by contact_id: {contact_id}")
                return None

            # 检查状态是否已过期
            if check_expiry and self._check_status_has_expired(enrichment_data):
                logger.warning(f"Contact {contact_id} enrichment request has expired, update status to failed")
                self._update_status_data(contact_id, enrichment_data.get("request_id", ""), "failed", "Request expired")
                enrichment_data["status"] = "failed"
                enrichment_data["error"] = "Request expired"
                enrichment_data["updated_at"] = datetime.now().isoformat()

            return enrichment_data

        except Exception as e:
            logger.error(f"Get enrichment data by contact_id {contact_id} failed: {e}", exc_info=True)
            raise Exception(f"Get enrichment data by contact_id failed: {str(e)}")

    def _check_status_has_expired(self, contact_enrichment_status: ContactEnrichmentStatus) -> bool:
        """检查status是否已过期（网络等原因导致未收到回调）"""
        if contact_enrichment_status["status"] == "pending":
            now = datetime.now()
            updated_at = contact_enrichment_status["updated_at"]
            if isinstance(updated_at, str):
                updated_at = datetime.fromisoformat(updated_at)
            return now - updated_at > timedelta(minutes=10)  # 10分钟未收到回调，则认为状态已过期
        return False

    def _update_status_data(
        self,
        contact_id: str,
        request_id: str,
        status: str,
        error: Optional[str] = None,
        phone_info: Optional[Dict[str, Any]] = None,
    ) -> None:
        """内部方法：更新联系人扩充状态数据"""
        try:
            update_data: Dict[str, Any] = {
                "_id": request_id,  # 使用 request_id 作为 _id
                "contact_id": contact_id,
                "request_id": request_id,
                "status": status,
                "updated_at": datetime.now().isoformat(),
            }

            # 只在有数据时才设置这些字段
            if phone_info:
                update_data["phone_info"] = phone_info

            if error is not None:
                update_data["error"] = error
            elif status != "failed":
                # 非失败状态时清除错误信息
                pass  # 不设置error字段，让它保持之前的值或为空
            else:
                # 确保failed状态有error字段
                if error is None:
                    update_data["error"] = "Unknown error"

            # 构建更新操作
            update_operations = {"$set": update_data}
            if error is None and status != "failed":
                # 清除错误信息
                update_operations["$unset"] = {"error": ""}

            self.collection.update_one(
                filter={"_id": request_id},
                update=update_operations,
                upsert=True,
            )
            logger.debug(f"Updated contact {contact_id} enrichment status to: {status}")

        except Exception as e:
            logger.error(f"Update contact {contact_id} enrichment status failed: {e}", exc_info=True)
            raise Exception(f"Update contact {contact_id} enrichment status failed: {str(e)}")

    def update_contact_enrichment_status(
        self,
        contact_id: str,
        request_id: str,
        status: str,
        error: Optional[str] = None,
        phone_info: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        更新联系人扩充状态

        Args:
            contact_id: 联系人ID
            request_id: 请求ID
            status: 状态 (pending, success, failed)
            error: 错误信息（可选）
            phone_info: 电话信息（可选）

        Returns:
            更新后的完整数据
        """
        self._update_status_data(contact_id, request_id, status, error, phone_info)

        # 返回更新后的完整数据
        updated_data = self.get_contact_enrichment_data(contact_id, check_expiry=False)
        if updated_data is None:
            raise Exception(f"Can't get contact {contact_id} data after update")
        return updated_data

    def store_enriched_data(
        self, contact_id: str, request_id: str, status: str, enriched_data: Any, owner: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        存储Apollo联系人扩充数据

        Args:
            contact_id: 联系人ID
            request_id: 请求ID
            status: 状态
            enriched_data: Apollo扩充的数据

        Returns:
            存储后的完整数据
        """
        try:
            # 统一转换enriched_data为字典格式
            if hasattr(enriched_data, "model_dump"):
                enriched_data = enriched_data.model_dump()
            elif hasattr(enriched_data, "dict"):
                enriched_data = enriched_data.dict()

            update_data = {
                "_id": request_id,  # 使用 request_id 作为 _id
                "contact_id": contact_id,
                "request_id": request_id,
                "status": status,
                "updated_at": datetime.now().isoformat(),
                "latest_data": enriched_data,
                "error": None,  # 清除之前可能的错误信息
                "owner": owner,
            }

            self.collection.update_one(
                filter={"_id": request_id},
                update={"$set": update_data},
                upsert=True,
            )

            logger.info(f"Stored enrichment data for contact {contact_id}, status: {status}")

            # 直接返回数据，不需要重新查询
            result = self.collection.find_one({"_id": request_id})
            if result is None:
                raise Exception(f"Can't get contact {contact_id} data after store")
            return result

        except Exception as e:
            logger.error(f"Store enrichment data for contact {contact_id} failed: {e}", exc_info=True)
            raise Exception(f"Store enrichment data for contact {contact_id} failed: {str(e)}")


# create global contact enrichment requests instance
contact_enrichment_requests = ContactEnrichmentRequests()
