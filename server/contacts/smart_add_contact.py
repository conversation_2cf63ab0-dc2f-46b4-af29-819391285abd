import json
from urllib.parse import urlparse

from fastapi import APIRouter
from fastapi.responses import StreamingResponse
from loguru import logger
from pydantic import BaseModel, Field

from agent.tools.apollo import apollo
from agent.tools.linkedin import agentic_search_people
from server.common_types import StreamMessage, StreamMessageType
from zoho.accounts_api import fetch_accounts_info_by_account_id
from zoho.contacts_api import add_contacts_to_zoho_use_origin_api
from zoho.data_transform import safe_str, transform_apollo_contact_data_to_zoho_contact_data
from zoho.get_existing_contacts import get_existing_contacts

router = APIRouter(tags=["contacts"])


def normalize_linkedin_url(url: str) -> str:
    """
    Normalize LinkedIn URL by removing protocol, www prefix, and converting to lowercase.

    Args:
        url: The LinkedIn URL to normalize

    Returns:
        Normalized URL without protocol and www prefix
    """
    if not url:
        return ""

    # Convert to lowercase first
    url = url.lower()

    # Add protocol if missing to help urlparse work correctly
    if not url.startswith(("http://", "https://", "//")):
        url = f"https://{url}"

    # Parse the URL and extract the netloc + path
    parsed = urlparse(url)

    # Remove www prefix from domain
    domain = parsed.netloc
    if domain.startswith("www."):
        domain = domain[4:]

    # Return domain + path, removing trailing slash
    return f"{domain}{parsed.path}".rstrip("/")


class SmartAddContactRequest(BaseModel):
    user_input: str = Field(..., description="用户查询的联系人信息")
    account_id: str = Field(..., description="Zoho 账户 ID，用于获取公司信息")
    user_info: dict = Field(..., description="用户信息，包含 username 和 email 等字段")
    request_headers: dict = Field(..., description="请求头信息")


@router.post("/contact/smart-add")
async def smart_add_contact(request: SmartAddContactRequest) -> StreamingResponse:
    """
    Smartly adds a contact based on the user's query and account ID.

    Args:
        user_input: The user's query for searching contacts.
        account_id: The Zoho account ID.
        user_info: Information about the user making the request.
    """

    def _build_stream_message(message: StreamMessage) -> str:
        return f"data: {json.dumps(message.model_dump(exclude_none=True), ensure_ascii=False, default=str)}\n\n"

    async def main_processor():
        try:
            user_input = request.user_input
            account_id = request.account_id
            request_headers = request.request_headers

            yield _build_stream_message(
                StreamMessage.from_thinking(
                    content="We are searching for contacts based on your input. Please wait.",
                )
            )

            zoho_account_info = await fetch_accounts_info_by_account_id(account_id)
            if not zoho_account_info:
                yield _build_stream_message(
                    StreamMessage.from_exception(
                        Exception(f"Cannot find Zoho account info for account_id: {account_id}")
                    )
                )
                return

            company_name = zoho_account_info.get("name")
            if not company_name:
                yield _build_stream_message(
                    StreamMessage.from_exception(
                        Exception(f"Missing company name in Zoho account info for account_id: {account_id}")
                    )
                )
                return
            enhanced_query = f"Find people at {company_name} who match: {user_input}"
            logger.info(f"Enhanced search query: {enhanced_query}")

            searched_person = await agentic_search_people(enhanced_query)

            no_find_error_msg = f"No matching contacts were found in {company_name}."
            logger.info(f"searched_person: {searched_person}")
            if not searched_person or not searched_person.profile_url:
                error_msg = StreamMessage.from_exception(Exception(no_find_error_msg))
                yield _build_stream_message(error_msg)
                return

            yield _build_stream_message(
                StreamMessage.from_thinking(
                    content="We have found a contact and are checking whether it already exists in the CRM.",
                )
            )

            existing_contacts = await get_existing_contacts(company_name, account_id)

            logger.info(f"existing_contacts: {existing_contacts}")
            logger.info(f"existing_contacts len: {len(existing_contacts)}")

            if existing_contacts and len(existing_contacts) > 0:
                existing_contact = next(
                    (
                        contact
                        for contact in existing_contacts
                        if normalize_linkedin_url(contact.get("linkedin_url", ""))
                        == normalize_linkedin_url(searched_person.profile_url)
                    ),
                    None,
                )

                if existing_contact:
                    existing_error_msg = StreamMessage.from_exception(
                        Exception(f"The contact {searched_person.full_name} already exists in {company_name}.")
                    )
                    existing_error_msg.data = existing_contact
                    yield _build_stream_message(existing_error_msg)
                    return

            yield _build_stream_message(
                StreamMessage.from_thinking(
                    content=f"We are completing the {searched_person.full_name}’s information. Please wait.",
                )
            )

            enriched_contacts = await apollo.enrich_person(
                linkedin_url=searched_person.profile_url,
            )

            if not enriched_contacts:
                error_msg = StreamMessage.from_exception(Exception(no_find_error_msg))
                yield _build_stream_message(error_msg)
                return

            yield _build_stream_message(
                StreamMessage.from_thinking(
                    content=f"The contact({searched_person.full_name}) information has been completed and is being added to the CRM.",  # noqa: E501
                )
            )
            logger.info(f"enriched_contacts: {enriched_contacts}")

            cleaned_contact = {key: safe_str(value) for key, value in enriched_contacts.items()}

            # transform data
            transform_zoho_contact = await transform_apollo_contact_data_to_zoho_contact_data(
                apollo_contact_data=cleaned_contact, default_data={}
            )

            logger.info(f"transform_zoho_contact: {transform_zoho_contact}")

            success_contacts = await add_contacts_to_zoho_use_origin_api(
                account_id=account_id,
                contacts=[transform_zoho_contact],
                request_headers=request_headers,
                owner_id=zoho_account_info.get("owner", {}).get("id"),
            )

            logger.info(f"success_contacts: {success_contacts}")

            if success_contacts and len(success_contacts) > 0:
                finish_msg = StreamMessage.from_data(success_contacts[0])
                finish_msg.type = StreamMessageType.FINISH
                yield _build_stream_message(finish_msg)
            else:
                error_msg = StreamMessage.from_exception(Exception(no_find_error_msg))
                yield _build_stream_message(error_msg)

            return

        except Exception as e:
            logger.error(f"Error in smart_add_contact: {e}", exc_info=True)
            yield _build_stream_message(StreamMessage.from_exception(e))

    return StreamingResponse(
        content=main_processor(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Access-Control-Allow-Origin": "*",
            "Access-Control-Allow-Headers": "Cache-Control",
        },
    )
