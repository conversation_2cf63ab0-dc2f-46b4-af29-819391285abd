from contextlib import contextmanager
from contextvars import ContextV<PERSON>
from typing import Any, Generator, Optional
from uuid import UUID

from langchain_core.callbacks import Async<PERSON><PERSON>back<PERSON>and<PERSON>
from langchain_core.messages import BaseMessage
from langchain_core.tracers.context import register_configure_hook
from loguru import logger

from server.common_types import StreamMessage
from server.task.task_manager import get_current_task_id, task_manager


def _assign_name(name: Optional[str], serialized: Optional[dict[str, Any]]) -> str:
    """Assign a name to a run."""
    if name is not None:
        return name
    if serialized is not None:
        if "name" in serialized:
            return serialized["name"]
        if "id" in serialized:
            return serialized["id"][-1]
    return "Unnamed"


def _message_metadata(metadata: Optional[dict[str, Any]]) -> dict[str, Any]:
    # filter out metadata keys that start with 'langgraph'
    metadata = metadata or {}
    filtered_metadata = {k: v for k, v in metadata.items() if (not k.startswith("langgraph") and k != "checkpoint_ns")}
    return filtered_metadata or None


class TaskCallbackHandler(AsyncCallbackHandler):
    def __init__(self, task_id: str | None = None):
        self.task_id = task_id or get_current_task_id()
        assert self.task_id, "task_id is required"

        self.explanations: list[str] = []

    async def on_tool_start(
        self,
        serialized: dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        inputs: Optional[dict[str, Any]] = None,
        name: Optional[str] = None,
        **kwargs: Any,
    ) -> None:
        name = _assign_name(name, serialized)
        if inputs and (explanation := inputs.get("explanation", None)):
            if explanation and isinstance(explanation, str):
                if explanation not in self.explanations:
                    # Send the explanation as a message
                    message = StreamMessage.from_thinking(explanation)
                    message.tags = tags
                    message.metadata = _message_metadata(metadata)

                    await task_manager.send_message(self.task_id, message)
                    self.explanations.append(explanation)

        logger.debug(f"on_tool_start: {name}")
        return None

    async def on_custom_event(
        self,
        name: str,
        data: Any,
        *,
        run_id: UUID,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        if name == "thinking" and isinstance(data, str):
            message = StreamMessage.from_thinking(data)
            message.tags = tags
            message.metadata = _message_metadata(metadata)

            await task_manager.send_message(self.task_id, message)

        elif name == "data" and isinstance(data, dict):
            message = StreamMessage.from_data(data)
            await task_manager.send_message(self.task_id, message)

        return None

    async def on_chat_model_start(
        self,
        serialized: dict[str, Any],
        messages: list[list[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        pass


task_callback_var: ContextVar[Optional[TaskCallbackHandler]] = ContextVar("task_callback", default=None)
register_configure_hook(task_callback_var, True)


@contextmanager
def task_callback(task_id: str) -> Generator[TaskCallbackHandler, None, None]:
    """Get the task callback handler in a context manager.
    which conveniently exposes token and cost information.

    Returns:
        TaskCallbackHandler: The task callback handler.

    Example:
        >>> with task_callback() as cb:
        ...     # Use the task callback handler
    """
    cb = TaskCallbackHandler(task_id)
    task_callback_var.set(cb)
    yield cb
    task_callback_var.set(None)
