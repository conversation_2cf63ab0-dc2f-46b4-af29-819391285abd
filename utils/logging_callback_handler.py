import json
from contextlib import contextmanager
from contextvars import ContextVar
from json import JSONDecodeError
from typing import Any, AsyncIterator, Generator, Iterator, Optional, TypeVar, Union, cast, override
from uuid import UUID

from langchain_core.callbacks.base import Async<PERSON><PERSON>back<PERSON>and<PERSON>
from langchain_core.messages import AIMessage, BaseMessage, BaseMessageChunk, ToolMessage
from langchain_core.messages.base import get_msg_title_repr
from langchain_core.messages.tool import ToolCallChunk
from langchain_core.outputs import ChatGenerationChunk, GenerationChunk, LLMResult
from langchain_core.tracers._streaming import _StreamingCallbackHandler
from langchain_core.tracers.context import register_configure_hook
from loguru import logger

from .messages import create_text_summary, message_content, message_thinking_content

T = TypeVar("T")


def tavily_search_pretty_repr(content: str) -> str:
    """Pretty print tavily search result."""
    try:
        search_result = json.loads(content)
    except JSONDecodeError:
        return content

    if isinstance(search_result, list):
        results = search_result
    else:
        results = search_result.get("results", [])

    if not results:
        return "No search results found."

    # format main search results
    formatted_results = []
    for i, result in enumerate(results[:3], 1):  # show top 3 results
        title = result.get("title", "No title")
        url = result.get("url", "No URL")
        content = result.get("content", "No content")
        content_snippet = create_text_summary(content, 200)
        score = result.get("score", 0)

        formatted_result = f"{i}. {title}\n   URL: {url}\n   Content: {content_snippet}\n   Score: {score:.3f}"
        formatted_results.append(formatted_result)

    if isinstance(search_result, list):
        return "\n\n".join(formatted_results)

    # add query info
    query = search_result.get("query", "Unknown query")
    response_time = search_result.get("response_time", 0)

    # combine all info
    result_text = f"Query: {query}\n"
    if "answer" in search_result:
        result_text += f"Answer: {search_result.get('answer', 'No answer')}\n"
    if search_result.get("follow_up_questions"):
        result_text += f"Follow Up Questions: {search_result.get('follow_up_questions')}\n"
    result_text += f"Response Time: {response_time:.2f}s\n"
    result_text += f"Total Results: {len(results)}\n\n"
    result_text += "\n\n".join(formatted_results)

    # add images info if available
    images = search_result.get("images", [])
    if images:
        result_text += f"\n\nImages found: {len(images)}"

    return result_text


def get_message_finish_reason(message: AIMessage) -> str:
    """Get the finish reason of a message."""
    if message.response_metadata and "finish_reason" in message.response_metadata:
        return message.response_metadata["finish_reason"]
    return "unknown"


class LoggingCallbackHandler(AsyncCallbackHandler, _StreamingCallbackHandler):
    """A callback handler that prints streaming events in a human-friendly way."""

    last_message_chunk: Optional[BaseMessageChunk] = None
    last_ai_message: Optional[AIMessage] = None
    is_thinking: bool = False
    logger = logger.opt(colors=True)

    def __init__(self, quiet: bool = True, *args: Any, **kwargs: Any) -> None:
        super().__init__(*args, **kwargs)
        self.runs: dict[UUID, dict[str, Any]] = {}
        self.quiet = quiet

    async def tap_output_aiter(self, run_id: UUID, output: AsyncIterator[T]) -> AsyncIterator[T]:
        async for chunk in output:
            yield chunk

    def tap_output_iter(self, run_id: UUID, output: Iterator[T]) -> Iterator[T]:
        return output

    def on_message_chunk(self, chunk: BaseMessageChunk) -> None:
        """Handle a message chunk."""
        # print(chunk)
        if not self.last_message_chunk:
            # start of a new message chunk
            self.last_message_chunk = chunk
            self.logger.debug(f"<light-white>\n{get_msg_title_repr('AI Message Chunk')}</light-white>")
        elif self.last_message_chunk.id != chunk.id:
            # printing a streaming chunk, skip the parallel AI message stream
            # to avoid mixing up the message content of parallel stream
            return

        thinking = message_thinking_content(chunk)
        if thinking is not None and not self.is_thinking:
            # o3 start of thinking
            self.is_thinking = True
            print("<think>", flush=True)

        if thinking:
            print(thinking, end="", flush=True)

        content = chunk.text()
        # handle the thinking end
        if (content or chunk.tool_call_chunks) and self.is_thinking:
            self.is_thinking = False
            print("\n</think>\n", flush=True)

        if content:
            print(content, end="", flush=True)

        if chunk.tool_call_chunks:
            self._handle_tool_call_chunks(chunk.tool_call_chunks)

        if chunk.response_metadata and "model_name" in chunk.response_metadata:
            print("\n")
            self.last_message_chunk = None

    def _handle_tool_call_chunks(self, tool_call_chunks: list[ToolCallChunk]) -> None:
        """Handle tool call chunks in the message chunk.

        Args:
            tool_call_chunks: list of tool call chunks
        """
        for tool_call_chunk in tool_call_chunks:
            if tool_call_chunk["name"]:
                print(
                    f"\nTool Name: {tool_call_chunk['name']} ({tool_call_chunk['id']})\nArgs:\n  ",
                    end="",
                    flush=True,
                )
            if tool_call_chunk["args"]:
                # decode unicode escape sequences in the args
                args = tool_call_chunk["args"]
                if "\\u" in args:
                    args = args.encode().decode("unicode_escape")
                print(args, end="", flush=True)

    @override
    async def on_llm_new_token(
        self,
        token: str,
        *,
        chunk: Optional[Union[GenerationChunk, ChatGenerationChunk]] = None,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        **kwargs: Any,
    ) -> Any:
        if self.quiet:
            if "tool" in self.get_full_run_name(parent_run_id):
                return

        if isinstance(chunk, ChatGenerationChunk):
            message_chunk = cast("ChatGenerationChunk", chunk).message
            self.on_message_chunk(message_chunk)

    @override
    async def on_llm_start(
        self,
        serialized: dict[str, Any],
        prompts: list[str],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Run when LLM starts running.

        .. ATTENTION::
            This method is called for non-chat models (regular LLMs). If you're
            implementing a handler for a chat model, you should use
            ``on_chat_model_start`` instead.

        Args:
            serialized (dict[str, Any]): The serialized LLM.
            prompts (list[str]): The prompts.
            run_id (UUID): The run ID. This is the ID of the current run.
            parent_run_id (UUID): The parent run ID. This is the ID of the parent run.
            tags (Optional[list[str]]): The tags.
            metadata (Optional[dict[str, Any]]): The metadata.
            kwargs (Any): Additional keyword arguments.
        """
        self.runs[run_id] = {
            "name": self.get_langchain_run_name(serialized, **kwargs),
            "run_id": run_id,
            "parent_run_id": parent_run_id,
            "metadata": metadata,
        }

    @override
    async def on_llm_end(
        self, response: LLMResult, *, run_id: UUID, parent_run_id: Optional[UUID] = None, **kwargs: Any
    ) -> Any:
        del self.runs[run_id]

        if self.quiet:
            if "tool" in self.get_full_run_name(parent_run_id):
                return

        message: BaseMessage | None = None
        for gen in response.generations:
            for chunk in gen:
                message = chunk.message
                break
        if message and isinstance(message, AIMessage):
            self.on_chat_model_end(message)

    async def on_chat_model_start(
        self,
        serialized: dict[str, Any],
        messages: list[list[BaseMessage]],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        name = self.get_langchain_run_name(serialized, **kwargs)
        self.runs[run_id] = {
            "name": name,
            "run_id": run_id,
            "parent_run_id": parent_run_id,
            "metadata": metadata,
        }

    def on_chat_model_end(self, message: AIMessage) -> None:
        """Handle completion of a chat model message."""

        if self.last_message_chunk and message.id == self.last_message_chunk.id:
            self.last_message_chunk = None

        self.last_ai_message = message

        if message.tool_calls:
            # check for a special 'think' tool
            if message.tool_calls[0]["name"] == "think":
                return

            content = AIMessage(content="", tool_calls=message.tool_calls).pretty_repr()
            content = content.replace("<", "\\<")
            self.logger.info(f"\n<cyan>{content}</cyan>")
        else:
            content = message_content(message).replace("<", "\\<")
            if content:
                self.logger.info(f"\n<light-blue>{get_msg_title_repr('AI Message')}\n{content}</light-blue>")

        stop_reason = get_message_finish_reason(message)
        if stop_reason and stop_reason.lower() not in ["stop", "tool_calls", "unknown"]:
            finish_message = message.response_metadata.get("finish_reason_message", "")
            self.logger.warning(
                f"AI message finished with reason: {stop_reason}" + (f", {finish_message}" if finish_message else "")
            )

    async def on_chain_start(
        self,
        serialized: dict[str, Any],
        inputs: dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Run when a chain starts running.

        Args:
            serialized (dict[str, Any]): The serialized chain.
            inputs (dict[str, Any]): The inputs.
            run_id (UUID): The run ID. This is the ID of the current run.
            parent_run_id (UUID): The parent run ID. This is the ID of the parent run.
            tags (Optional[list[str]]): The tags.
            metadata (Optional[dict[str, Any]]): The metadata.
            kwargs (Any): Additional keyword arguments.
        """
        self.runs[run_id] = {
            "name": self.get_langchain_run_name(serialized, **kwargs),
            "run_id": run_id,
            "parent_run_id": parent_run_id,
            "metadata": metadata,
        }

    async def on_chain_end(
        self,
        outputs: dict[str, Any],
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        tags: Optional[list[str]] = None,
        **kwargs: Any,
    ) -> None:
        """Run when a chain ends running.

        Args:
            outputs (dict[str, Any]): The outputs of the chain.
            run_id (UUID): The run ID. This is the ID of the current run.
            parent_run_id (UUID): The parent run ID. This is the ID of the parent run.
            tags (Optional[list[str]]): The tags.
            kwargs (Any): Additional keyword arguments.
        """
        del self.runs[run_id]

    def get_langchain_run_name(self, serialized: Optional[dict[str, Any]], **kwargs: Any) -> str:
        """Retrieve the name of a serialized LangChain runnable.

        The prioritization for the determination of the run name is as follows:
        - The value assigned to the "name" key in `kwargs`.
        - The value assigned to the "name" key in `serialized`.
        - The last entry of the value assigned to the "id" key in `serialized`.
        - "<unknown>".

        Args:
            serialized (Optional[Dict[str, Any]]): A dictionary containing the runnable's serialized data.
            **kwargs (Any): Additional keyword arguments, potentially including the 'name' override.

        Returns:
            str: The determined name of the Langchain runnable.
        """
        if "name" in kwargs and kwargs["name"] is not None:
            return str(kwargs["name"])

        if serialized is None:
            return "<unknown>"

        try:
            return str(serialized["name"])
        except (KeyError, TypeError):
            pass

        try:
            return str(serialized["id"][-1])
        except (KeyError, TypeError):
            pass

        return "<unknown>"

    def get_full_run_name(self, run_id: UUID) -> str:
        """Get the full hierarchical run name by traversing all parent runs."""
        run = self.runs.get(run_id)
        if not run:
            return "<unknown>"

        names = []
        current_run = run

        # traverse up the hierarchy collecting names
        while current_run:
            names.append(current_run["name"])
            parent_run_id = current_run.get("parent_run_id")
            if parent_run_id:
                current_run = self.runs.get(parent_run_id)
            else:
                current_run = None

        # reverse to get root -> leaf order and join with " / "
        names.reverse()
        return " / ".join(names)

    @override
    async def on_tool_start(
        self,
        serialized: dict[str, Any],
        input_str: str,
        *,
        run_id: UUID,
        parent_run_id: Optional[UUID] = None,
        metadata: Optional[dict[str, Any]] = None,
        inputs: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> Any:
        """Handle the start of a tool call."""

        name = self.get_langchain_run_name(serialized, **kwargs)
        self.runs[run_id] = {
            "name": name,
            "inputs": inputs,
            "run_id": run_id,
            "parent_run_id": parent_run_id,
            "metadata": metadata,
        }

        if name == "think":
            self.logger.info(f"<light-black>\\<think>{inputs['thought']}\\</think></light-black>")

    @override
    async def on_tool_end(self, output: Any, *, run_id: UUID, **kwargs: Any) -> None:
        """Handle the end of a tool call."""
        del self.runs[run_id]

        if isinstance(output, ToolMessage):
            tool_name = output.name

            if tool_name == "tavily_search":
                self.logger.info(f"Tool {tool_name} result:\n<cyan>{tavily_search_pretty_repr(output.content)}</cyan>")
            elif output.name not in ["think", "final_answer"]:
                if self.last_ai_message and self.last_ai_message.content == output.content:
                    return
                content_brief = create_text_summary(output.content, 1024)
                content_brief = content_brief.replace("<", "\\<")
                # self.logger.info(f"Tool {output.name} result:\n<cyan>{content_brief}</cyan>")
                tool_message = ToolMessage(
                    content=content_brief,
                    name=output.name,
                    id=output.id,
                    tool_call_id=output.tool_call_id,
                )
                if tool_message.status == "error":
                    self.logger.error(f"Tool {output.name} error: {tool_message.content}")
                else:
                    self.logger.info(f"\n<cyan>{tool_message.pretty_repr()}</cyan>")

    # ------------------------------------------------------------------
    # custom event hook -------------------------------------------------
    # ------------------------------------------------------------------
    @override
    async def on_custom_event(
        self,
        name: str,
        data: Any,
        *,
        run_id: UUID,
        tags: Optional[list[str]] = None,
        metadata: Optional[dict[str, Any]] = None,
        **kwargs: Any,
    ) -> None:
        """Handle any custom event emitted by the runnable stack."""
        try:
            self.logger.info(f"<light-black>custom event: {name}: {data}</light-black>")
        except Exception as exc:  # pragma: no cover – never crash caller
            self.logger.warning(f"LoggingCallbackHandler failed to handle custom event {name}: {exc}", exc_info=True)


logging_callback_var: ContextVar[Optional[LoggingCallbackHandler]] = ContextVar("logging_callback", default=None)
register_configure_hook(logging_callback_var, True)


@contextmanager
def logging_callback(quiet: bool = True) -> Generator[LoggingCallbackHandler, None, None]:
    """enable the LoggingCallbackHandler in a context manager.

    Example:
        >>> with logging_callback():
        ...     # Use the LoggingCallbackHandler
    """
    cb = LoggingCallbackHandler(quiet=quiet)
    logging_callback_var.set(cb)
    yield cb
    logging_callback_var.set(None)
