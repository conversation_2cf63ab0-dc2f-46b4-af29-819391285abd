import os
from typing import Literal

from langchain.chat_models import init_chat_model
from langchain.chat_models.base import BaseChatModel
from loguru import logger

from utils import from_env, secret_from_env

type ModelAlias = Literal[
    "claude-4-sonnet",
    "claude-3-7-sonnet",
    "claude-3.7-sonnet",
    "claude-3.5-haiku",
    "qwen",
    "gemini-2.5-flash",
    "gemini-2.5-flash-instant",
    "gemini-2.5-pro",
    "o4-mini-high",
    "gpt-4.1",
    "gpt-4o",
    "gpt-4o-mini",
]

logged_messages = set()


def _log_once(message: str, level: str = "DEBUG"):
    if message not in logged_messages:
        logged_messages.add(message)
        logger.log(level, message)


def _get_default_thinking_budget(thinking_budget: int | None = None) -> int | None:
    if thinking_budget is not None:
        return int(thinking_budget)
    budget = from_env("LLM_THINKING_BUDGET", default=None)
    return int(budget) if budget and budget.isdigit() else None


def _claude_thinking_value(thinking_budget: int | None = None) -> dict:
    budget_tokens = _get_default_thinking_budget(thinking_budget)
    match budget_tokens:
        case 0:
            return {"type": "disabled"}
        case budget_tokens if budget_tokens is not None and budget_tokens < 1024:
            return {"type": "enabled", "budget_tokens": 1024}
        case _:
            return {"type": "enabled", "budget_tokens": budget_tokens}


def _claude_default_thinking_params(thinking_budget: int | None = None) -> dict:
    return {
        "temperature": 1,
        "additional_model_request_fields": {
            "thinking": _claude_thinking_value(thinking_budget),
        },
    }


def _parse_model_alias(model: str, **kwargs) -> str | tuple[str, dict]:
    """parse model alias to actual model name"""
    thinking_budget = kwargs.get("thinking_budget")
    match model:
        case "claude-4-sonnet-thinking":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-4-sonnet":
            return "us.anthropic.claude-sonnet-4-20250514-v1:0"
        case "claude-3-7-sonnet" | "claude-3.7-sonnet":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0"
        case "claude-3.7-sonnet-thinking":
            return "us.anthropic.claude-3-7-sonnet-20250219-v1:0", _claude_default_thinking_params(thinking_budget)
        case "claude-3.5-haiku":
            return "us.anthropic.claude-3-5-haiku-20241022-v1:0"
        case "qwen":
            return "qwen-max"
        # case "gemini-2.5-flash":
        #     return "gemini-2.5-flash-preview-05-20"
        # case "gemini-2.5-pro":
        #     return "gemini-2.5-pro-preview-06-05"
        # return "gemini-2.5-pro-preview-05-06"
        # return "gemini-2.5-pro-preview-03-25"
        case "gemini-2.5-flash-instant":
            return "gemini-2.5-flash", {"thinking_budget": 0, "include_thoughts": False}
        case "gemini-2.5-flash-lite":
            return "gemini-2.5-flash-lite-preview-06-17"
        case model if model.startswith("openrouter:"):
            return model.split(":", 1)[1], {
                "model_provider": "openai",
                "base_url": "https://openrouter.ai/api/v1",
                "api_key": secret_from_env("OPENROUTER_API_KEY", default=None),
            }
        case model if model.startswith("aliyun:"):
            return model.split(":", 1)[1], {
                "model_provider": "openai",
                "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
            }
        case "gpt-4.1":
            return "gpt-4o"
        case "gpt-4o" | "gpt-4o-mini":
            return model
        case model if model.startswith("o") or model.startswith("gpt-5"):
            use_responses_api = kwargs.get("use_responses_api", True)
            reasoning_effort = None
            reasoning_summary = "auto" if use_responses_api else None
            if model.endswith("-high"):
                reasoning_effort = "high"
                model = model[:-5]
            elif model.endswith("-low"):
                reasoning_effort = "low"
                model = model[:-4]
            elif model.endswith("-minimal"):
                reasoning_effort = "minimal"
                model = model[:-8]

            return model, {"reasoning_effort": reasoning_effort} if not use_responses_api else {
                "use_responses_api": use_responses_api,
                # "text": {"verbosity": "high"},
                "reasoning": {"effort": reasoning_effort, "summary": reasoning_summary},
            }
        case model if model.startswith("qwen") and model.endswith("-thinking"):
            return model[:-9], {
                "extra_body": {
                    "enable_thinking": True,
                    "thinking_budget": _get_default_thinking_budget(thinking_budget),
                }
            }
        case _:
            return model


def _parse_model_provider(model: str, /, **kwargs: dict) -> str | tuple[str, dict]:
    """parse model to determine the appropriate model provider and its params"""
    if "claude" in model:
        https_proxy = from_env(["HTTPS_PROXY", "BEDROCK_PROXY"], default="")
        _log_once(f"using http proxy: {https_proxy} to access anthropic model {model}")
        from botocore.config import Config

        return "bedrock_converse", {
            "provider": "anthropic",
            # "credentials_profile_name": from_env("AWS_PROFILE", default="inhand"),
            "aws_access_key_id": from_env("AWS_ACCESS_KEY_ID", default=None),
            "aws_secret_access_key": secret_from_env("AWS_SECRET_ACCESS_KEY", default=None),
            "region_name": from_env("AWS_REGION", default="us-west-2"),
            "config": Config(proxies={"https": https_proxy}),
            "base_url": from_env("BEDROCK_BASE_URL", default=None),
        }
    elif model.startswith("qwen") or model.startswith("deepseek"):
        return "openai", {
            "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
            "api_key": secret_from_env("DASHSCOPE_API_KEY", default=None),
            "openai_proxy": "",
        }
    elif model.startswith("gemini"):
        tb_param = kwargs.get("thinking_budget")
        thinking_budget = _get_default_thinking_budget(tb_param if isinstance(tb_param, int) else None)
        model_params = {
            "thinking_budget": thinking_budget,
            "include_thoughts": kwargs.get("include_thoughts", thinking_budget != 0 and thinking_budget is not None),
        }

        api_endpoint = from_env(["GEMINI_API_ENDPOINT", "API_ENDPOINT_PROXY"], default=None)
        if api_endpoint:
            model_params["client_options"] = {
                "api_endpoint": api_endpoint,
            }
        else:
            if grpc_proxy := from_env(["GRPC_PROXY", "GEMINI_PROXY"], default=""):
                _log_once(f"using grpc proxy: {grpc_proxy} to access google model {model}")
                os.environ["grpc_proxy"] = grpc_proxy

        return "google_genai", model_params
    elif model.startswith(("gpt", "o")):
        return "openai"
    else:
        # default to openai for unknown models
        return "openai"


def _init_model_params(model: str, /, **kwargs) -> tuple[str, dict]:
    """initialize model parameters based on model type"""
    model_kwargs = dict(kwargs)  # Create a copy to avoid type issues

    match _parse_model_alias(model, **model_kwargs):
        case (model_name, alias_params):
            model = model_name
            model_kwargs = alias_params | model_kwargs
        case model_name:
            model = model_name

    thinking_budget = model_kwargs.pop("thinking_budget", None)
    if "model_provider" not in model_kwargs:
        match _parse_model_provider(model, thinking_budget=thinking_budget, **model_kwargs):
            case (model_provider, provider_params):
                model_kwargs = provider_params | model_kwargs
                # the model provider may override the model name
                if "model" in provider_params:
                    model = provider_params["model"]
            case model_provider:
                pass

        model_kwargs["model_provider"] = model_provider
    else:
        model_provider = model_kwargs["model_provider"]
        if not isinstance(model_provider, str):
            model_provider = "openai"  # fallback to default

    if "max_tokens" not in model_kwargs:
        max_tokens_str = from_env("LLM_MAX_TOKENS", default="2048")
        model_kwargs["max_tokens"] = int(max_tokens_str) if max_tokens_str else 2048

    if _support_temperature(model_provider, model):
        # set default temperature
        if "temperature" not in model_kwargs:
            temp_str = from_env("LLM_TEMPERATURE", default="0")
            model_kwargs["temperature"] = float(temp_str) if temp_str else 0.0
    else:
        model_kwargs.pop("temperature", None)

    return model, model_kwargs


def _support_temperature(model_provider: str, model: str) -> bool:
    if model_provider == "openai" and (model.startswith("gpt-5") or model.startswith("o")):
        return False
    return True


def _default_temperature(model_provider: str, model: str) -> float | None:
    if model_provider == "openai" and (model.startswith("gpt-5") or model.startswith("o")):
        return None
    temp_str = from_env("LLM_TEMPERATURE", default="0")
    return float(temp_str) if temp_str else 0.0


def init_model(
    model: ModelAlias | str,
    thinking_budget: int | None = None,
    use_responses_api: bool | None = None,
    **kwargs,
) -> BaseChatModel:
    """
    initialize a chat model with the given parameters

    Args:
        model: model name or alias
        use_responses_api: optional flag to use responses api for reasoning models, used for openai models only, default to False
        thinking_budget: thinking budget in tokens, set to 0 to disable thinking, default to None

    Returns:
        BaseChatModel: initialized chat model
    """  # noqa: E501
    if use_responses_api is not None:
        kwargs["use_responses_api"] = use_responses_api
    if thinking_budget is not None:
        kwargs["thinking_budget"] = thinking_budget

    model_name, init_model_params = _init_model_params(model, **kwargs)
    logger.debug(f"initializing model {model_name} with params {init_model_params}")
    llm = init_chat_model(model=model_name, **init_model_params)
    if "_model_name" not in llm:
        llm._model_name = model_name
    return llm
