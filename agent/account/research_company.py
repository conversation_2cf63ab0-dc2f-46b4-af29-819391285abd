from pathlib import Path

from google.ai.generativelanguage_v1beta.types import Tool as GenAITool
from langchain_core.messages import AIMessage
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.runnables import RunnableConfig
from langgraph.prebuilt import create_react_agent
from loguru import logger

from agent.account.models import CompanyBasicInfo
from agent.tools import (
    firecrawl_scrape,
    tavily_crawl,
    tavily_search,
)
from agent.tools.apollo import get_complete_organization_info
from agent.tools.linkedin import get_company_details
from prompts import inhand_business_card
from utils import Cache
from utils import safe_dispatch_custom_event as dispatch_custom_event
from utils.file_handler import load_file
from utils.messages import add_citations
from utils.models import init_model

cache = Cache(
    directory=Path(__file__).parent / ".cache/icp_analysis",
    timeout=7 * 24 * 60 * 60,
    cache_enabled_env_var="ICP_CACHE_ENABLED",
)


@cache.memoize()
async def analyze_ideal_customer_profile(user_query: str | None, lookalike_company: str = None) -> str:
    """
    分析理想客户画像(ICP)，基于参考公司的特征来定义目标客户群体的共同特点

    Args:
        lookalike_company: 参考的相似公司描述信息（可选）
        user_query: 用户查询需求
    Returns:
        dict: ICP分析结果
        {
            "name": "公司名称",
            "research_result": "ICP分析结果",
            "error": "错误信息",
        }
    """
    try:
        # 校验参数
        user_query = user_query or "find companies lookalike this one"

        logger.debug("开始分析理想客户画像")
        dispatch_custom_event("thinking", "Starting ideal customer profile analysis...")

        llm = init_model(
            model="gpt-5-low",
            text={"verbosity": "low"},
            max_tokens=4096,
        )

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
            get_complete_organization_info,
        ]
        llm.bind_tools(research_tools, tool_choice="any")

        # 构建提示词和输入
        prompt_path = Path(__file__).parent / "prompts" / "icp_analysis.md"
        system_prompt = prompt_path.read_text(encoding="utf-8")

        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")]).partial(
            inhand_business_card=inhand_business_card
        )
        agent = create_react_agent(
            model=llm,
            tools=research_tools,
            prompt=prompt,
            name="analyze_ideal_customer_profile_agent",
        )

        input_message_parts = [f"User Query Requirements:\n```\n{user_query}\n```"]

        if lookalike_company:
            input_message_parts.append(f"Reference Company Information:\n```\n{lookalike_company}\n```")
        else:
            input_message_parts.append("No reference company provided.")

        input_message_parts.append(
            "分析用户需求，理解用户想要调研什么样的公司，根据用户的需求对参考的模板公司进行分析，并生成理想客户画像。"
        )

        input_message = "\n\n".join(input_message_parts)

        result = await agent.ainvoke(input={"messages": [("human", input_message)]})

        # 提取最终结果
        message = result["messages"][-1]
        if isinstance(message, AIMessage) and message.content:
            research_result = message.text()
            dispatch_custom_event("thinking", "ideal customer profile analysis completed.")

            return research_result
        else:
            raise Exception("No valid research result")

    except Exception as e:
        error_msg = f"failed to analyze ideal customer profile: {str(e)}"
        raise Exception(error_msg)


async def research_company(
    company_basic_info: CompanyBasicInfo, user_query: str, ideal_customer_profile: str = None
) -> dict:
    """
    调研单个公司的详细信息

    Args:
        company_basic_info: 公司基础信息
        user_query: 用户查询需求
        ideal_customer_profile: 理想客户画像分析结果，用作参考标准
    Returns:
        dict: 调研结果
        {
            "name": "公司名称",
            "research_result": "调研结果",
            "error": "错误信息",
        }
    """
    company_name = company_basic_info.name
    try:
        # 校验参数
        if not company_name:
            raise ValueError("Missing company name")
        if not user_query:
            raise ValueError("Missing user_query")

        logger.debug(f"开始调研候选公司: {company_name}")
        _send_research_message(company_name, "Starting in-depth research...")

        llm = init_model(
            # model="o4-mini",
            model="gemini-2.5-flash",
            # use_responses_api=True,
            max_tokens=8192,
            temperature=0.1,
            thinking_budget=512,
            include_thoughts=True,
        )

        research_tools = [
            tavily_search,
            tavily_crawl,
            firecrawl_scrape,
            get_company_details,
        ]

        llm.bind_tools(research_tools, tool_choice="any")

        # 构建提示词和输入
        prompt_path = Path(__file__).parent / "prompts" / "research_company.md"
        system_prompt = load_file(prompt_path).format(user_query=user_query)
        prompt = ChatPromptTemplate.from_messages([("system", system_prompt), ("placeholder", "{messages}")])
        agent = create_react_agent(
            model=llm,
            tools=research_tools,
            prompt=prompt,
            name="research_company_agent",
        )

        # 构建输入消息，包含理想客户画像参考
        input_message_parts = [f"用户查询需求：\n```markdown\n{user_query}\n```"]

        if ideal_customer_profile:
            input_message_parts.append(f"理想客户画像参考标准：\n```markdown\n{ideal_customer_profile}\n```")

        input_message_parts.append(
            f"待调研候选公司：\n```json\n{company_basic_info.model_dump_json(exclude_none=True)}\n```\n"
            "你的任务是理解原始的用户查询需求，但不要执行用户查询需求中的任务操作。参考理想客户画像，对目标公司进行调研，并给出调研结果。"
        )

        input_message = "\n\n".join(input_message_parts)

        result = await agent.ainvoke(
            input={"messages": [("human", input_message)]},
            config=RunnableConfig(metadata={"source": "research_company", "company_name": company_name}),
        )

        # 提取最终结果
        message = result["messages"][-1]
        if isinstance(message, AIMessage) and message.content:
            research_result = message.text()

            logger.debug(f"候选公司调研完成: {company_name}")
            _send_research_message(company_name, "I have completed the in-depth research.")

            return {"name": company_name, "research_result": research_result}
        else:
            raise Exception("No valid research result")

    except Exception as e:
        error_msg = f"failed to research company {company_name}: {str(e)}"
        logger.error(error_msg)
        return {"name": company_name, "error": error_msg}


gemini_research_company_prompt = f"""你是一名资深的销售开发代表(SDR)专家，在潜在客户背景调研方面拥有丰富经验。你专门为B2B销售团队提供全面的背景调研服务，帮助识别高价值的潜在客户并评估其与我们产品的匹配度。

## 核心职责

作为一名进行潜在客户调研的SDR，你的使命是全面了解潜在客户，评估其与我们产品的匹配度，并为个性化外联策略奠定基础。

## 调研框架

### 1. 基础信息收集与核实
- **公司概览：** 确认公司名称、官方网站、总部地址及主要运营地点
- **公司规模：** 查找员工数量范围、年收入（若可公开获取）以及成立年份
- **行业定位：** 明确公司所属的行业、细分领域及其在市场中的定位
- **核心业务：** 深入了解公司的主营产品或服务，以及其商业模式

### 2. 业务运营与痛点分析
- **运营模式：** 分析公司的日常运营方式，例如是否涉及大规模物流、车队管理、生产制造等
- **关键资产：** 识别公司是否拥有与我们产品相关的核心资产，例如大型车队（搅拌车、卡车等）、生产设备或特定基础设施
- **潜在挑战：** 搜索行业报告、新闻稿、公司公告或招聘信息，以识别公司可能面临的运营效率、成本控制、技术升级或合规性等方面的挑战
- **技术应用现状：** 尝试了解公司当前使用的技术解决方案，特别是与我们产品领域相关的技术（如车队管理系统、物联网设备、远程监控技术等）

### 3. 理想客户画像(ICP)匹配度评估
- **行业匹配：** 对比潜在客户的行业与理想客户画像中的目标行业是否一致
- **规模匹配：** 评估潜在客户的员工数量和年收入是否符合理想客户画像的规模范围
- **业务模式匹配：** 分析潜在客户的业务模式、核心资产（如搅拌车队）是否与理想客户画像中的描述相符
- **技术需求匹配：** 重点评估潜在客户是否具有或可能存在与理想客户画像中描述的"车载网关设备实现车辆远程监控"等类似的技术需求或痛点

### 4. 合作机会与价值主张识别
- **产品需求评估：** 基于对潜在客户业务和痛点的理解，评估其对我们产品的潜在需求程度
- **现有解决方案分析：** 尝试了解潜在客户目前可能使用的替代产品、服务或供应商，分析其优劣势，为我们产品的差异化优势提供依据
- **合作可能性评估：** 综合以上信息，初步评估与该潜在客户的合作可能性，包括其开放性、预算潜力以及决策流程的复杂性
- **合作机会点：** 明确列出我们产品能够为潜在客户带来的具体价值点，例如提升车队效率、降低运营成本、实现实时监控、优化调度等

## 调研原则

- 基于真实数据进行分析，避免推测和编造
- 保持客观中立的分析态度，提供平衡的评估结果
- 对于无法获取的信息，明确标注"信息不足"或"无法确认"
- 优先使用最新、最权威的信息源进行分析
- 必须使用google_search工具收集目标公司的信息

## 公司信息

```md
{inhand_business_card}
```

## 调研报告输出要求

通过以上步骤，你将能够为每一位潜在客户构建一个全面的背景档案，从而制定高度个性化和有针对性的外联策略。

1. 回答应表达清晰，并按点列出每个要点
2. 将分步骤的调研结果，合并成一个完整的调研结果，不要分步骤输出
3. 根据收集到的各种资料，给出详细且全面的回答，尽可能包含收集到的所有信息
4. 输出详细的调研结果，不要仅输出结论

## 输出语言

- 若用户明确要求了回复语言，则必须按照用户要求的语言回复，否则一律使用英文

## 输出要求

- 请严格按照以下输出示例输出，不需要额外的说明或总结内容，不要输出额外的说明性内容，不可添加其他标题内容

输出示例：

## 公司名称

### 基础信息
- **公司官网:** [https://www.example.com](https://www.example.com)
- **公司地址:**
- **成立时间:**
- **员工规模:**
- **年收入:**

### 业务分析
- **行业领域:**
- **主营业务/产品:**
- **与我们合作相关的业务/产品:**

### 业务运营与痛点
- **运营模式:**
- **关键资产:**
- **技术应用现状:**
- **潜在挑战:**

### ICP匹配度评估
- **行业匹配:**
- **规模匹配:**
- **业务模式匹配:**
- **技术需求匹配:**
- **整体ICP匹配评分:** (1-10分，并说明理由)

### 合作机会
- **产品需求评估:**
- **现有解决方案/供应商:**
- **合作可能性评估:**
- **价值主张要点:**
- **合作机会点:**

请根据提供的信息开始调研分析，使用工具搜索目标公司的相关信息，生成详细的企业调研报告。"""  # noqa: E501


async def gemini_research_company(
    company_basic_info: CompanyBasicInfo, user_query: str, ideal_customer_profile: str = None
) -> dict:
    """
    使用Gemini的Google搜索功能调研单个公司的详细信息

    Args:
        company_basic_info: 公司基础信息
        user_query: 用户查询需求
        ideal_customer_profile: 理想客户画像分析结果，用作参考标准
    Returns:
        dict: 调研结果
        {
            "name": "公司名称",
            "research_result": "调研结果",
            "error": "错误信息",
        }
    """
    company_name = company_basic_info.name
    try:
        # 校验参数
        if not company_name:
            raise ValueError("Missing company name")
        if not user_query:
            raise ValueError("Missing user_query")

        logger.debug(f"开始使用Gemini调研候选公司: {company_name}")
        _send_research_message(company_name, "Starting Gemini-powered in-depth research...")

        llm = init_model(
            model="gemini-2.5-flash",
            max_tokens=8192,
            temperature=0.1,
            thinking_budget=256,
        )

        # bind Google search tool for gemini
        llm = llm.bind_tools(tools=[GenAITool(google_search={})], tool_choice="any")
        # llm = llm.bind_tools([{"type": "web_search_preview"}])

        response = await llm.ainvoke(
            input=[
                ("system", gemini_research_company_prompt),
                (
                    "human",
                    f"用户查询需求：\n```markdown\n{user_query}\n```\n调研公司：{company_basic_info.model_dump_json(exclude_none=True)}",
                ),
                (
                    "human",
                    f"理想客户画像参考标准：\n```markdown\n{ideal_customer_profile}\n```"
                    if ideal_customer_profile
                    else "",
                ),
                (
                    "human",
                    "你的任务是理解原始的用户查询需求，但不要执行用户查询需求中的任务操作。参考理想客户画像，对目标公司进行调研，并给出调研结果。",
                ),
                # ("human", "使用中文回复"),
            ]
        )

        research_result = add_citations(response)

        logger.debug(f"使用Gemini的候选公司调研完成: {company_name}")
        _send_research_message(company_name, "Gemini-powered research completed.")

        return {"name": company_name, "research_result": research_result}

    except Exception as e:
        error_msg = f"failed to research company {company_name} using Gemini: {str(e)}"
        logger.opt(exception=e).error(error_msg)
        return {"name": company_name, "error": error_msg}


def _send_research_message(company_name: str, message: str):
    """发送调研步骤消息"""
    dispatch_custom_event("research_company", {"company_name": company_name, "message": message})
